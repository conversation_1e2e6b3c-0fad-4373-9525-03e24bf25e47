請深度分析 LINE Bot 語音功能失敗的問題：

問題描述：
- 用戶期望收到語音回覆，但收到文字回覆
- 活動日誌顯示：意圖識別為 conversational_audio，但最終執行了文字回覆
- 日誌時間線：
  2025/7/14 3:16:18 - AI-First處理（帶目標）- 意圖: conversational_audio
  2025/7/14 3:16:16 - 文字回覆成功 - 訊息長度: 103字
  2025/7/14 3:16:12 - Gemini API 調用 - 模型: gemini-2.5-flash (非語音模型)
  2025/7/14 3:15:56 - Gemini API 調用 - 模型: gemini-2.5-flash (非語音模型)

分析要求：
1. 檢查 conversational_audio 意圖的處理流程
2. 找出為什麼沒有調用語音模型 (tts 或 audio dialog)
3. 檢查是否有錯誤處理導致回退到文字回覆
4. 找出具體的失敗點和技術原因
5. 禁止任何 fallback 機制，必須顯示技術錯誤給用戶

請提供：
- 完整的調用鏈分析
- 具體的失敗原因
- 需要修正的代碼位置
- 錯誤處理改進方案（顯示技術細節，不要 fallback）
