# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a sophisticated LINE Bot built on Google Apps Script (GAS) with an AI-First architecture. The bot integrates with Gemini AI and features a modular design with 30+ functional modules supporting hot-swappable components and feature toggles.

## Key Commands

### Testing and Building
```bash
# Health Check (run in GAS console)
systemHealthCheck()

# Complete System Test
runCompleteSystemTest()

# Quick System Test  
quickSystemTest()

# Test Feature Toggle System
testFeatureToggleSystem()

# Test Module Manager
testModuleManager()

# Test Safety Net
testSafetyNet()
```

### Development Workflow Commands
```bash
# Initialize the system
initializeSheets()

# Setup feature toggles
setupFeatureToggleSystem()

# Auto-manage modules based on feature toggles
autoManageModules()

# Run comprehensive safety net check
runSafetyNetCheck()

# Generate system status report
generateSystemStatusReport()

# Validate module mapping integrity
validateModuleMapping()
```

### Deployment Commands
```bash
# Deploy as Web App in Google Apps Script
# 1. Click "Deploy" → "New deployment"
# 2. Choose "Web application"  
# 3. Execute as: "Me"
# 4. Access: "Anyone"

# The main entry point is doPost() for LINE webhooks
# and doGet() for web browser access
```

## Architecture Overview

### Core System Files
- **core_main.gs** - Main entry point and system orchestration
- **core_config.gs** - Configuration management and Google Sheets integration
- **core_utils.gs** - Shared utility functions
- **core_helpers.gs** - Helper functions
- **core_module_manager.gs** - Hot-swappable module management system
- **core_feature_toggle.gs** - Feature toggle management with spreadsheet control
- **core_model_config.gs** - AI model configuration

### Module Categories
- **modules_line_*.gs** - LINE Bot integration (webhook, processors, media)
- **modules_ai_*.gs** - AI processing (Gemini integration, prompts, features)
- **modules_image_*.gs** - Image generation and processing
- **modules_audio_*.gs** - Audio/TTS functionality  
- **modules_file_*.gs** - File handling and analysis
- **modules_group_*.gs** - Group chat tracking and management
- **modules_note_*.gs** - Note-taking and memory systems
- **modules_smart_responder*.gs** - Intelligent response system

### Google Apps Script Constraints
- **Global Namespace**: All .gs files share global scope - functions can be called directly across files
- **No ES6 Modules**: Cannot use import/export statements
- **No fetch()**: Use UrlFetchApp.fetch() instead
- **No URLSearchParams**: Use custom parseQueryString() function
- **Storage**: Google Sheets as database, Google Drive for file storage

## Key Design Patterns

### AI-First Architecture
The system prioritizes intelligent prompt engineering over complex logic, delegating sophisticated decision-making to Gemini AI models.

### Hot-Swappable Modules
- Modules can be enabled/disabled via feature toggles in Google Sheets
- `MODULE_FILE_MAPPING` in core_module_manager.gs defines file associations
- Files are logically moved between "active" and "sleeping" states

### Safety Net System
- Automated validation of module mappings and dependencies
- Detection of missing files, duplicate functions, and inconsistencies
- Auto-repair suggestions and preview functionality

### Three-Phase Development Workflow
1. **Pre-Modification**: Use `preModificationCheck()` and validate dependencies
2. **During Modification**: Update file headers, maintain mapping consistency  
3. **Post-Modification**: Run `postModificationCheck()` and safety net validation

## Development Guidelines

### Before Making Changes
1. **Always run** `preModificationCheck(['target_file.gs'], ['function_name'])`
2. **Search existing code** for similar functionality to avoid duplication
3. **Check feature toggles** in `core_feature_toggle.gs` 
4. **Validate module mappings** if adding new modules

### During Development
1. **Update file headers** with modification date
2. **Use existing patterns** - study similar modules for conventions
3. **Follow AI-First principles** - use smart prompts over complex logic
4. **Test incrementally** using the provided test functions

### After Changes
1. **Run** `postModificationCheck(['modified_file.gs'], ['new_functions'])`
2. **Execute** `runSafetyNetCheck()` to validate system integrity
3. **Test functionality** with appropriate test functions
4. **Update documentation** if introducing new patterns

### Critical Rules
- **Never create duplicate functions** - the system will detect and flag these
- **Always update** `MODULE_FILE_MAPPING` when adding new modules
- **Maintain** single source of truth for all configurations
- **Use traditional Chinese** for user-facing messages and responses
- **Test in GAS environment** before deployment

## Testing Strategy

### Test File Naming
- Persistent tests: `original_function_debug.gs`
- Disposable tests: `original_function_disposable.gs`  
- Location: `/_test` folder

### Key Test Functions
```javascript
// System health
systemHealthCheck()
quickSystemTest()

// Feature testing
testFeatureToggleSystem()
testModuleManager()
testSafetyNet()

// Module testing
testAIProcessor()
testImageGeneration()
testGroupTracking()
```

### Test Standards
- Core functions: 100% pass rate required
- Module functions: ≥90% pass rate acceptable
- Zero syntax errors
- Successful `clasp push` deployment

## Configuration Management

### Google Sheets Configuration
The APIKEY sheet contains all configuration values:
- LINE Channel Access Token (B2)
- LINE Channel Secret (B3)  
- Gemini API Key (B4)
- Feature toggles (功能開關_FEATURE_NAME format)

### Feature Toggle Control
Feature toggles can be controlled via:
1. **Google Sheets** (recommended): Set 功能開關_FEATURE_NAME = true/false
2. **Code constants**: Modify FEATURE_TOGGLES in core_feature_toggle.gs

### Adding New Features
1. Add to `FEATURE_TOGGLES` in core_feature_toggle.gs
2. Create module files following naming convention
3. Update `MODULE_FILE_MAPPING` in core_module_manager.gs
4. Test with safety net validation

## Deployment Notes

This is a Google Apps Script project that auto-deploys via GitHub integration. The system:
- Uses Git for version control with GitHub sync
- Deploys as a Web App with public access
- Serves as a LINE Bot webhook endpoint
- Stores data in Google Sheets and Google Drive

The main webhook entry point is `doPost()` in modules_line_webhook.gs, which routes to various processing modules based on message type and AI-determined intent.

## Emergency Procedures

If the system becomes unstable:
1. Run `systemHealthCheck()` to diagnose issues
2. Execute `runSafetyNetCheck()` for comprehensive analysis  
3. Use `resetAllFileStates()` to clear module state if needed
4. Check `generateSystemStatusReport()` for detailed status
5. Contact system administrators with generated reports

The safety net system provides automated diagnosis and repair suggestions for most common issues.
